{"name": "atrean-news", "version": "1.0.0", "main": "server.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon --exec ts-node ./app/server.ts", "prod": "node build/server.js"}, "_moduleAliases": {"@": "app"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "module-alias": "^2.2.3", "mongoose": "^8.16.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}