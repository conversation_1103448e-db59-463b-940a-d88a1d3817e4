{"compilerOptions": {"target": "es2020", "lib": ["es2020"], "typeRoots": ["./node_modules/@types", "app/types"], "module": "commonjs", "rootDir": "./app", "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true}, "include": ["app/**/*"], "exclude": ["node_modules", "dist"]}