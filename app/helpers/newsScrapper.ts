import { chromium } from 'playwright';
import OpenAI from 'openai';
import { config } from '../config';

const openai_client = new OpenAI({ apiKey: config.openAiApiKey });

export async function scrapNews(url: string) {
  let browser;
  try {
    // Validate URL
    if (!url || !url.startsWith('http')) {
      throw new Error('Invalid URL provided');
    }

    // Launch browser with better settings for scraping
    browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
      ],
    });

    const page = await browser.newPage();

    // Set user agent to avoid bot detection
    await page.setExtraHTTPHeaders({
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    });

    // Try different wait strategies
    try {
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 60000, // 60 seconds timeout
      });
    } catch (timeoutError) {
      console.log('First attempt failed, trying with load event...');
      await page.goto(url, {
        waitUntil: 'load',
        timeout: 45000, // 45 seconds timeout
      });
    }

    // Wait a bit for dynamic content to load
    await page.waitForTimeout(3000);

    // Extract text content with better error handling
    const textContent = await page.evaluate(() => {
      // Remove script and style elements
      const scripts = document.querySelectorAll('script, style');
      scripts.forEach((el: Element) => el.remove());

      // Get main content areas first, fallback to body
      const contentSelectors = [
        'article',
        '[role="main"]',
        '.content',
        '.article-content',
        '.post-content',
        'main',
        'body',
      ];

      for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element && (element as HTMLElement).innerText.trim().length > 100) {
          return (element as HTMLElement).innerText.trim();
        }
      }

      return document.body.innerText.trim();
    });

    // Extract image URLs with better filtering
    const imageUrls = await page.$$eval(
      'img',
      imgs =>
        imgs
          .map(img => img.src)
          .filter(src => src && src.startsWith('http') && !src.includes('data:'))
          .slice(0, 10), // Limit to first 10 images
    );

    console.log(`Scraped ${textContent.length} characters of text and ${imageUrls.length} images`);

    if (!textContent || textContent.length < 100) {
      throw new Error('Insufficient content scraped from the page');
    }

    const summary = await summerizeNews(textContent, imageUrls);
    return summary;
  } catch (error) {
    console.log(`Error in scrapNews: ${error}`);
    throw error;
  } finally {
    // Ensure browser is always closed
    if (browser) {
      await browser.close();
    }
  }
}

const summerizeNews = async (textContent: string, urls: string[] | null) => {
  try {
    const systemPrompt = `You are a helpful summerization agent who is an expert in summarizing news articles. You will be provided the scrapped text data from a news article and your task will be to create a proper summary of the text data. You should not include any other information in the summary. Summarize the following text in a few sentences and return in json format, Do not add any extra texts above or below the response:\n
    output format:
    {
      "title": "title of the article", <String title of the article>
      "summary": "summary of the text data", <String summary of the text data>
      "images" : ["image url 1", "image url 2"] ,<List of images in the article>
      "category": "category of the news article" <String category of the article>
    }
    `;
    let userPrompt = `Summarize the following text in a few sentences:\n${textContent} Return in JSON format`;

    if (urls && urls.length > 0) {
      userPrompt += `\n The images in the article are: ${urls.join(', ')}`;
    }

    const response = await openai_client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: 'json_object' },
    });

    if (!response.choices[0].message.content) {
      throw new Error('No content in the response');
    }
    const result_str: string = response.choices[0].message.content;
    const result = JSON.parse(result_str);
    return result;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
