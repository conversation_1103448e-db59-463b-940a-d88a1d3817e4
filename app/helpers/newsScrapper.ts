import { chromium } from 'playwright';
import OpenAI from 'openai';
import { config } from '../config';

const openai_client = new OpenAI({ apiKey: config.openAiApiKey });

export async function scrapNews(url: string) {
  try {
    const browser = await chromium.launch();
    const page = await browser.newPage();

    await page.goto(url, { waitUntil: 'networkidle' });
    const textContent = await page.evaluate((document: any) => document.body.innerText);
    const imageUrls = await page.$$eval('img', imgs => imgs.map(img => img.src));
    const summary = await summerizeNews(textContent, imageUrls);
    await browser.close();
    return summary;
  } catch (error) {
    console.log(`Error in scrapNews: ${error}`);
    throw error;
  }
}

const summerizeNews = async (textContent: string, urls: string[] | null) => {
  try {
    const systemPrompt = `You are a helpful summerization agent who is an expert in summarizing news articles. You will be provided the scrapped text data from a news article and your task will be to create a proper summary of the text data. You should not include any other information in the summary. Summarize the following text in a few sentences and return in json format, Do not add any extra texts above or below the response:\n
    output format:
    {
      "summary": "summary of the text data", <String summary of the text data>
      "images" : ["image url 1", "image url 2"] <List of images in the article>
    }
    `;
    let userPrompt = `Summarize the following text in a few sentences:\n${textContent} Return in JSON format`;

    if (urls && urls.length > 0) {
      userPrompt += `\n The images in the article are: ${urls.join(', ')}`;
    }

    const response = await openai_client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: 'json_object' },
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
