import { connect, set, disconnect, Connection } from 'mongoose';
import { config } from '../config';

export const initDb = async (): Promise<Connection> => {
  try {
    if (!config.dbUrl) {
      throw new Error('Database URL is not defined in config.');
    }

    const db = await connect(config.dbUrl);
    console.log('Connected to MongoDB');

    if (process.env.NODE_ENV === 'development') {
      set('debug', true);
    }

    return db.connection;
  } catch (e) {
    console.log(e);
    await disconnect();
    throw e;
  }
};
