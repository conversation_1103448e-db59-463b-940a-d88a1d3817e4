import { Request, Response } from 'express';
import { NewsResponse, Article } from '../types/news';
import axios from 'axios';
import { config } from '../config';
import { scrapNews } from '../helpers/newsScrapper';

// This function is used to search the list of news article from news api
export const searchNews = async (req: Request, res: Response): Promise<any> => {
  try {
    const { keyWord, page = '1', pageSize = '10' } = req.query;
    if (!keyWord) {
      return res.status(400).json({ message: 'keyWord is required' });
    }
    const newsData = await axios.get(`${config.newsApiBaseUrl}`, {
      params: {
        q: keyWord,
        apiKey: config.newsApiKey,
        page: parseInt(page as string, 1),
        pageSize: parseInt(pageSize as string, 10),
      },
    });
    if (newsData.status !== 200) {
      throw new Error('News API is not responding');
    }
    const newsList: NewsResponse = newsData.data;
    return res.status(200).json(newsList);
  } catch (error: any) {
    console.log(error);
    let errorMsg = error.msg || error;
    return res.status(500).json({ message: errorMsg });
  }
};

export const scrapAndSaveNews = async (req: Request, res: Response): Promise<any> => {
  try {
    const { url } = req.body;
    if (!url) {
      return res.status(400).json({ message: 'url is required' });
    }
    try {
      const summary = await scrapNews(url);
      return res.status(200).json({ summary });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ message: error });
    }
  } catch (error: any) {
    console.log(error);
    let errorMsg = error.msg || error;
    return res.status(500).json({ message: errorMsg });
  }
};
