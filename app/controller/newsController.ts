import { Request, Response } from 'express';
import { searchNewsRequest } from '../types/news';
import axios from 'axios';
import { config } from '../config';

export const searchNews = async (req: Request, res: Response): Promise<any> => {
  try {
    const { keyWord } = req.body as searchNewsRequest;

    const news = await axios.get(`${config.newsApiBaseUrl}?q=${keyWord}&apiKey=${config.newsApiKey}`);
    console.log(news.data);
    return 'hello';
  } catch (error: any) {
    console.log(error);
    let errorMsg = error.msg || error;
    return res.status(500).json({ message: errorMsg });
  }
};
