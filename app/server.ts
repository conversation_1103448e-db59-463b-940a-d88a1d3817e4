import express from 'express';
import cors from 'cors';
import 'module-alias/register';
import { config } from './config';
import { initDb } from './db/db';
import newsRouter from './routes/routes';

const app = express();

app.use(express.json());
app.use(cors());

function init_routes() {
  app.get('/', (req, res) => {
    res.send('Hello World!');
  });
  app.use('/api/news', newsRouter);
  app.use((err: any, req: any, res: any, next: any) => {
    res.status(500).json({ error: err.message || err });
    next();
  });
}

async function init() {
  try {
    await initDb();
    init_server();
    init_routes();
  } catch (e) {
    console.log(e);
    process.exit(1);
  }
}

function init_server() {
  const port = config.port;
  app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
  });
}

init();
