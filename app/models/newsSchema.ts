import { Schema, model } from 'mongoose';

export interface News {
  title: string;
  description: string;
  url: string;
  image: string | null;
  category: string | null;
}

const newsSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      required: false,
    },
    category: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  },
);

export const News = model('News', newsSchema);
