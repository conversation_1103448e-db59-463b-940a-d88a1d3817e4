import { Schema, model } from 'mongoose';

export interface NewsInterface {
  title: string;
  summary: string;
  url: string | null;
  images: string[] | null;
  category: string | null;
}

const newsSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    summary: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
    },
    images: {
      type: Array,
      required: false,
    },
    category: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  },
);

export const News = model('News', newsSchema);
